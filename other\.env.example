# High-Performance Transparent Proxy Server Configuration

# Server port (default: 8000)
PORT=8000

# Proxy URL prefix (default: /proxy/)
# The server will respond to requests matching this prefix
PROXY_PREFIX=/proxy/

# Domain whitelist configuration
# Use "*" to allow all domains (default)
# Use comma-separated list for specific domains
# Use "*.domain.com" for subdomain wildcards

# Examples:

# Allow all domains (default)
ALLOWED_DOMAINS=*

# Allow specific domains only
# ALLOWED_DOMAINS=api.github.com,httpbin.org,jsonplaceholder.typicode.com

# Allow domains with subdomain wildcards
# ALLOWED_DOMAINS=*.github.com,*.googleapis.com,api.openai.com

# Mixed configuration
# ALLOWED_DOMAINS=example.com,*.api-service.com,trusted-domain.org

# ====================================
# CORS (Cross-Origin Resource Sharing) Configuration
# ====================================

# Enable or disable CORS support (default: true)
# Set to "false" to disable CORS headers entirely
CORS_ENABLED=true

# CORS allowed origins (default: *)
# Use "*" to allow all origins
# Use specific domain for restricted access: "https://example.com"
# Use comma-separated list for multiple domains: "https://app1.com,https://app2.com"
CORS_ORIGIN=*

# CORS allowed HTTP methods (default: GET,POST,PUT,DELETE,OPTIONS,HEAD,PATCH)
# Customize based on your API requirements
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS,HEAD,PATCH

# CORS allowed headers (default: *)
# Use "*" to allow all headers
# Use specific headers: "Content-Type,Authorization,X-Requested-With"
CORS_HEADERS=*

# CORS preflight cache duration in seconds (default: 86400 = 24 hours)
# How long browsers can cache preflight responses
CORS_MAX_AGE=86400

# CORS credentials support (default: false)
# Set to "true" to allow credentials (cookies, authorization headers)
# Note: Cannot be used with CORS_ORIGIN=* for security reasons
CORS_CREDENTIALS=false

# ====================================
# CORS Configuration Examples
# ====================================

# Restrictive CORS for production
# CORS_ENABLED=true
# CORS_ORIGIN=https://myapp.com,https://www.myapp.com
# CORS_METHODS=GET,POST,PUT,DELETE
# CORS_HEADERS=Content-Type,Authorization
# CORS_CREDENTIALS=true

# Development CORS (allow everything)
# CORS_ENABLED=true
# CORS_ORIGIN=*
# CORS_METHODS=*
# CORS_HEADERS=*
# CORS_CREDENTIALS=false

# Disable CORS entirely
# CORS_ENABLED=false
