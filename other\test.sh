#!/bin/bash

# Test script for High-Performance Transparent Proxy Server

SERVER_URL="http://localhost:8000"
PROXY_PREFIX="/proxy/"

echo "🧪 Testing High-Performance Transparent Proxy Server"
echo "Server: $SERVER_URL"
echo "=========================================="

# Function to test a request
test_request() {
    local description="$1"
    local url="$2"
    local expected_status="$3"
    
    echo "📋 Test: $description"
    echo "URL: $url"
    
    response=$(curl -s -w "HTTP_STATUS:%{http_code}\nTIME_TOTAL:%{time_total}" "$url")
    status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
    time=$(echo "$response" | grep "TIME_TOTAL:" | cut -d: -f2)
    body=$(echo "$response" | grep -v "HTTP_STATUS:\|TIME_TOTAL:")
    
    if [ "$status" = "$expected_status" ]; then
        echo "✅ Status: $status (Expected: $expected_status)"
        echo "⏱️  Time: ${time}s"
    else
        echo "❌ Status: $status (Expected: $expected_status)"
    fi
    
    if [ ${#body} -gt 100 ]; then
        echo "📄 Response: ${body:0:100}..."
    else
        echo "📄 Response: $body"
    fi
    echo ""
}

# Test 1: Server info
test_request "Server Information" "$SERVER_URL/" "200"

# Test 2: Valid proxy request
test_request "Valid Proxy Request" "$SERVER_URL${PROXY_PREFIX}https://httpbin.org/get" "200"

# Test 3: Proxy with query parameters
test_request "Proxy with Query Parameters" "$SERVER_URL${PROXY_PREFIX}https://httpbin.org/get?test=123&proxy=deno" "200"

# Test 4: POST request
echo "📋 Test: POST Request with JSON Data"
echo "URL: $SERVER_URL${PROXY_PREFIX}https://httpbin.org/post"
response=$(curl -s -w "HTTP_STATUS:%{http_code}\nTIME_TOTAL:%{time_total}" \
    -X POST \
    -H "Content-Type: application/json" \
    -d '{"message":"Hello from Deno Proxy","timestamp":"'$(date -Iseconds)'"}' \
    "$SERVER_URL${PROXY_PREFIX}https://httpbin.org/post")

status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
time=$(echo "$response" | grep "TIME_TOTAL:" | cut -d: -f2)

if [ "$status" = "200" ]; then
    echo "✅ Status: $status"
    echo "⏱️  Time: ${time}s"
else
    echo "❌ Status: $status"
fi
echo ""

# Test 5: Invalid URL
test_request "Invalid URL Format" "$SERVER_URL${PROXY_PREFIX}not-a-valid-url" "502"

# Test 6: Performance test
echo "🚀 Performance Test: Multiple Concurrent Requests"
echo "Running 10 concurrent requests..."

start_time=$(date +%s.%N)
for i in {1..10}; do
    curl -s "$SERVER_URL${PROXY_PREFIX}https://httpbin.org/get?request=$i" > /dev/null &
done
wait
end_time=$(date +%s.%N)

duration=$(echo "$end_time - $start_time" | bc)
echo "✅ Completed 10 concurrent requests in ${duration}s"
echo "📊 Average: $(echo "scale=3; $duration / 10" | bc)s per request"
echo ""

echo "🎉 Testing completed!"
echo ""
echo "💡 Tips:"
echo "   - Check server logs for detailed information"
echo "   - Modify ALLOWED_DOMAINS to test domain restrictions"
echo "   - Use different PROXY_PREFIX values to test routing"
