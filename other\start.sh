#!/bin/bash

# High-Performance Transparent Proxy Server Startup Script

echo "🚀 Starting High-Performance Transparent Proxy Server..."

# Default configuration
DEFAULT_PORT=8000
DEFAULT_PROXY_PREFIX="/proxy/"
DEFAULT_ALLOWED_DOMAINS="*"

# Load environment variables with defaults
export PORT=${PORT:-$DEFAULT_PORT}
export PROXY_PREFIX=${PROXY_PREFIX:-$DEFAULT_PROXY_PREFIX}
export ALLOWED_DOMAINS=${ALLOWED_DOMAINS:-$DEFAULT_ALLOWED_DOMAINS}

echo "📋 Configuration:"
echo "   Port: $PORT"
echo "   Proxy Prefix: $PROXY_PREFIX"
echo "   Allowed Domains: $ALLOWED_DOMAINS"
echo ""

# Check if Deno is installed
if ! command -v deno &> /dev/null; then
    echo "❌ Deno is not installed. Please install Deno first:"
    echo "   curl -fsSL https://deno.land/install.sh | sh"
    exit 1
fi

# Check if main.ts exists
if [ ! -f "main.ts" ]; then
    echo "❌ main.ts not found in current directory"
    exit 1
fi

echo "✅ Starting server on port $PORT..."
echo "🌐 Access at: http://localhost:$PORT"
echo "📖 Usage: http://localhost:$PORT${PROXY_PREFIX}<target-url>"
echo ""
echo "Press Ctrl+C to stop the server"
echo "----------------------------------------"

# Start the server
deno run --allow-net --allow-env main.ts
