@echo off
REM High-Performance Transparent Proxy Server Startup Script for Windows

echo 🚀 Starting High-Performance Transparent Proxy Server...

REM Default configuration
set DEFAULT_PORT=8000
set DEFAULT_PROXY_PREFIX=/proxy/
set DEFAULT_ALLOWED_DOMAINS=*

REM Load environment variables with defaults
if "%PORT%"=="" set PORT=%DEFAULT_PORT%
if "%PROXY_PREFIX%"=="" set PROXY_PREFIX=%DEFAULT_PROXY_PREFIX%
if "%ALLOWED_DOMAINS%"=="" set ALLOWED_DOMAINS=%DEFAULT_ALLOWED_DOMAINS%

echo 📋 Configuration:
echo    Port: %PORT%
echo    Proxy Prefix: %PROXY_PREFIX%
echo    Allowed Domains: %ALLOWED_DOMAINS%
echo.

REM Check if Deno is installed
deno --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Deno is not installed. Please install Deno first:
    echo    https://deno.land/manual/getting_started/installation
    pause
    exit /b 1
)

REM Check if main.ts exists
if not exist "main.ts" (
    echo ❌ main.ts not found in current directory
    pause
    exit /b 1
)

echo ✅ Starting server on port %PORT%...
echo 🌐 Access at: http://localhost:%PORT%
echo 📖 Usage: http://localhost:%PORT%%PROXY_PREFIX%^<target-url^>
echo.
echo Press Ctrl+C to stop the server
echo ----------------------------------------

REM Start the server
deno run --allow-net --allow-env main.ts
