# 使用示例

## 基本使用

### 1. 启动服务器

```bash
# 使用默认配置
deno task start

# 或使用批处理文件 (Windows)
start.bat

# 或直接运行
deno run --allow-net --allow-env main.ts
```

### 2. 基本代理请求

```bash
# GET 请求
curl "http://localhost:8000/proxy/https://httpbin.org/get"

# 带查询参数
curl "http://localhost:8000/proxy/https://httpbin.org/get?name=test&value=123"

# POST 请求
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"message":"Hello World"}' \
  "http://localhost:8000/proxy/https://httpbin.org/post"
```

## 高级配置

### 1. 自定义代理前缀

```bash
# 设置环境变量
export PROXY_PREFIX="/api/forward/"

# 启动服务器
deno task start

# 使用新前缀
curl "http://localhost:8000/api/forward/https://api.github.com/users/octocat"
```

### 2. 域名白名单配置

```bash
# 只允许特定域名
export ALLOWED_DOMAINS="httpbin.org,api.github.com"

# 允许子域名
export ALLOWED_DOMAINS="*.github.com,*.googleapis.com"

# 混合配置
export ALLOWED_DOMAINS="httpbin.org,*.api.example.com,trusted-service.com"
```

## 实际应用场景

### 1. API 聚合服务

```javascript
// 前端代码示例
const apiProxy = "http://localhost:8000/proxy/";

// 调用不同的 API 服务
const githubData = await fetch(`${apiProxy}https://api.github.com/users/octocat`);
const weatherData = await fetch(`${apiProxy}https://api.openweathermap.org/data/2.5/weather?q=London`);
const newsData = await fetch(`${apiProxy}https://newsapi.org/v2/top-headlines?country=us`);
```

### 2. 跨域解决方案

```html
<!DOCTYPE html>
<html>
<head>
    <title>跨域代理示例</title>
</head>
<body>
    <script>
        // 解决跨域问题
        const proxyUrl = "http://localhost:8000/proxy/";
        
        async function fetchData() {
            try {
                const response = await fetch(proxyUrl + "https://api.example.com/data");
                const data = await response.json();
                console.log(data);
            } catch (error) {
                console.error("请求失败:", error);
            }
        }
        
        fetchData();
    </script>
</body>
</html>
```

### 3. 微服务网关

```bash
# 配置多个服务的代理
export ALLOWED_DOMAINS="user-service.internal,order-service.internal,payment-service.internal"
export PROXY_PREFIX="/gateway/"

# 客户端请求
curl "http://localhost:8000/gateway/http://user-service.internal/api/users/123"
curl "http://localhost:8000/gateway/http://order-service.internal/api/orders"
curl "http://localhost:8000/gateway/http://payment-service.internal/api/payments"
```

## 性能测试

### 1. 运行基准测试

```bash
# 启动服务器（终端1）
deno task start

# 运行基准测试（终端2）
deno run --allow-net benchmark.ts
```

### 2. 自定义性能测试

```bash
# 使用 Apache Bench
ab -n 1000 -c 10 "http://localhost:8000/proxy/https://httpbin.org/get"

# 使用 wrk
wrk -t12 -c400 -d30s "http://localhost:8000/proxy/https://httpbin.org/get"
```

## 监控和调试

### 1. 服务器状态检查

```bash
# 查看服务器信息
curl http://localhost:8000/

# 输出示例：
# Transparent Proxy Server
# 
# Usage: /proxy/<target-url>
# Example: /proxy/https://example.com/api/data
# 
# Allowed domains: *
# Environment variables:
# - PROXY_PREFIX: /proxy/
# - ALLOWED_DOMAINS: *
```

### 2. 错误处理测试

```bash
# 测试无效 URL
curl "http://localhost:8000/proxy/not-a-valid-url"

# 测试被禁止的域名（需要先设置 ALLOWED_DOMAINS）
export ALLOWED_DOMAINS="httpbin.org"
curl "http://localhost:8000/proxy/https://google.com"  # 应该被拒绝
```

## 部署示例

### 1. Docker 部署

```dockerfile
FROM denoland/deno:alpine

WORKDIR /app
COPY main.ts deno.json ./

# 设置环境变量
ENV ALLOWED_DOMAINS="*.api.company.com,trusted-service.com"
ENV PROXY_PREFIX="/proxy/"
ENV PORT=8000

EXPOSE 8000

CMD ["run", "--allow-net", "--allow-env", "main.ts"]
```

```bash
# 构建和运行
docker build -t transparent-proxy .
docker run -p 8000:8000 transparent-proxy
```

### 2. 系统服务部署

```ini
# /etc/systemd/system/transparent-proxy.service
[Unit]
Description=Transparent Proxy Server
After=network.target

[Service]
Type=simple
User=proxy
WorkingDirectory=/opt/transparent-proxy
Environment=ALLOWED_DOMAINS=*.api.company.com,trusted-service.com
Environment=PROXY_PREFIX=/proxy/
Environment=PORT=8000
ExecStart=/usr/local/bin/deno run --allow-net --allow-env main.ts
Restart=always

[Install]
WantedBy=multi-user.target
```

```bash
# 启用和启动服务
sudo systemctl enable transparent-proxy
sudo systemctl start transparent-proxy
sudo systemctl status transparent-proxy
```

## 故障排除

### 常见问题和解决方案

1. **端口被占用**
   ```bash
   # 检查端口使用情况
   netstat -tulpn | grep :8000
   
   # 使用不同端口
   export PORT=3000
   deno task start
   ```

2. **域名被拒绝**
   ```bash
   # 检查当前配置
   curl http://localhost:8000/
   
   # 更新域名白名单
   export ALLOWED_DOMAINS="*"  # 允许所有域名
   ```

3. **请求超时**
   ```bash
   # 检查目标服务是否可达
   curl -I https://target-service.com
   
   # 检查网络连接
   ping target-service.com
   ```

4. **内存使用过高**
   ```bash
   # 监控内存使用
   top -p $(pgrep -f "deno.*main.ts")
   
   # 检查是否有内存泄漏
   deno run --allow-net --allow-env --v8-flags=--expose-gc main.ts
   ```
