/**
 * Performance Benchmark Tool for Transparent Proxy Server
 * Tests various scenarios and measures performance metrics
 */

interface BenchmarkResult {
  testName: string;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  totalTime: number;
  averageTime: number;
  requestsPerSecond: number;
  minTime: number;
  maxTime: number;
}

class ProxyBenchmark {
  private serverUrl: string;
  private proxyPrefix: string;

  constructor(serverUrl = "http://localhost:8000", proxyPrefix = "/proxy/") {
    this.serverUrl = serverUrl;
    this.proxyPrefix = proxyPrefix;
  }

  /**
   * Run a single HTTP request and measure timing
   */
  private async measureRequest(url: string, options?: RequestInit): Promise<number> {
    const startTime = performance.now();
    try {
      const response = await fetch(url, options);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      await response.text(); // Consume response body
      return performance.now() - startTime;
    } catch (error) {
      console.error(`Request failed: ${error}`);
      throw error;
    }
  }

  /**
   * Run concurrent requests and collect metrics
   */
  private async runConcurrentTest(
    testName: string,
    url: string,
    concurrency: number,
    totalRequests: number,
    options?: RequestInit
  ): Promise<BenchmarkResult> {
    console.log(`\n🧪 Running: ${testName}`);
    console.log(`   Concurrency: ${concurrency}, Total Requests: ${totalRequests}`);

    const results: number[] = [];
    const errors: Error[] = [];
    const startTime = performance.now();

    // Create batches of concurrent requests
    const batchSize = concurrency;
    const batches = Math.ceil(totalRequests / batchSize);

    for (let batch = 0; batch < batches; batch++) {
      const batchStart = batch * batchSize;
      const batchEnd = Math.min(batchStart + batchSize, totalRequests);
      const batchRequests = batchEnd - batchStart;

      const promises = Array.from({ length: batchRequests }, async () => {
        try {
          const time = await this.measureRequest(url, options);
          return time;
        } catch (error) {
          throw error;
        }
      });

      try {
        const batchResults = await Promise.allSettled(promises);
        
        batchResults.forEach((result) => {
          if (result.status === 'fulfilled') {
            results.push(result.value);
          } else {
            errors.push(result.reason);
          }
        });

        // Progress indicator
        const progress = Math.round(((batch + 1) / batches) * 100);
        process.stdout.write(`\r   Progress: ${progress}%`);
      } catch (error) {
        console.error(`Batch ${batch} failed:`, error);
      }
    }

    const totalTime = performance.now() - startTime;
    console.log(`\n   Completed in ${(totalTime / 1000).toFixed(2)}s`);

    return {
      testName,
      totalRequests,
      successfulRequests: results.length,
      failedRequests: errors.length,
      totalTime: totalTime / 1000, // Convert to seconds
      averageTime: results.length > 0 ? results.reduce((a, b) => a + b, 0) / results.length : 0,
      requestsPerSecond: results.length / (totalTime / 1000),
      minTime: results.length > 0 ? Math.min(...results) : 0,
      maxTime: results.length > 0 ? Math.max(...results) : 0,
    };
  }

  /**
   * Test basic GET requests
   */
  async testBasicGet(): Promise<BenchmarkResult> {
    const url = `${this.serverUrl}${this.proxyPrefix}https://httpbin.org/get`;
    return this.runConcurrentTest("Basic GET Requests", url, 10, 100);
  }

  /**
   * Test POST requests with JSON payload
   */
  async testPostJson(): Promise<BenchmarkResult> {
    const url = `${this.serverUrl}${this.proxyPrefix}https://httpbin.org/post`;
    const options = {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ benchmark: true, timestamp: Date.now() }),
    };
    return this.runConcurrentTest("POST JSON Requests", url, 5, 50, options);
  }

  /**
   * Test high concurrency scenario
   */
  async testHighConcurrency(): Promise<BenchmarkResult> {
    const url = `${this.serverUrl}${this.proxyPrefix}https://httpbin.org/get`;
    return this.runConcurrentTest("High Concurrency Test", url, 50, 200);
  }

  /**
   * Test with query parameters
   */
  async testWithQueryParams(): Promise<BenchmarkResult> {
    const url = `${this.serverUrl}${this.proxyPrefix}https://httpbin.org/get?param1=value1&param2=value2&timestamp=${Date.now()}`;
    return this.runConcurrentTest("Requests with Query Parameters", url, 10, 100);
  }

  /**
   * Print benchmark results in a formatted table
   */
  private printResults(results: BenchmarkResult[]): void {
    console.log("\n" + "=".repeat(120));
    console.log("📊 BENCHMARK RESULTS");
    console.log("=".repeat(120));
    
    console.log(
      "Test Name".padEnd(30) +
      "Total".padEnd(8) +
      "Success".padEnd(8) +
      "Failed".padEnd(8) +
      "Avg Time".padEnd(12) +
      "Min Time".padEnd(12) +
      "Max Time".padEnd(12) +
      "RPS".padEnd(10) +
      "Total Time"
    );
    console.log("-".repeat(120));

    results.forEach((result) => {
      console.log(
        result.testName.padEnd(30) +
        result.totalRequests.toString().padEnd(8) +
        result.successfulRequests.toString().padEnd(8) +
        result.failedRequests.toString().padEnd(8) +
        `${result.averageTime.toFixed(2)}ms`.padEnd(12) +
        `${result.minTime.toFixed(2)}ms`.padEnd(12) +
        `${result.maxTime.toFixed(2)}ms`.padEnd(12) +
        result.requestsPerSecond.toFixed(1).padEnd(10) +
        `${result.totalTime.toFixed(2)}s`
      );
    });

    console.log("-".repeat(120));
    
    // Summary statistics
    const totalRequests = results.reduce((sum, r) => sum + r.totalRequests, 0);
    const totalSuccessful = results.reduce((sum, r) => sum + r.successfulRequests, 0);
    const totalFailed = results.reduce((sum, r) => sum + r.failedRequests, 0);
    const avgRPS = results.reduce((sum, r) => sum + r.requestsPerSecond, 0) / results.length;
    
    console.log(`📈 SUMMARY:`);
    console.log(`   Total Requests: ${totalRequests}`);
    console.log(`   Successful: ${totalSuccessful} (${((totalSuccessful/totalRequests)*100).toFixed(1)}%)`);
    console.log(`   Failed: ${totalFailed} (${((totalFailed/totalRequests)*100).toFixed(1)}%)`);
    console.log(`   Average RPS: ${avgRPS.toFixed(1)}`);
  }

  /**
   * Run all benchmark tests
   */
  async runAllTests(): Promise<void> {
    console.log("🚀 Starting Proxy Server Benchmark");
    console.log(`Server: ${this.serverUrl}`);
    console.log(`Proxy Prefix: ${this.proxyPrefix}`);

    // Check if server is running
    try {
      await fetch(this.serverUrl);
    } catch (error) {
      console.error("❌ Server is not running. Please start the proxy server first.");
      Deno.exit(1);
    }

    const results: BenchmarkResult[] = [];

    try {
      results.push(await this.testBasicGet());
      results.push(await this.testWithQueryParams());
      results.push(await this.testPostJson());
      results.push(await this.testHighConcurrency());
    } catch (error) {
      console.error("❌ Benchmark failed:", error);
      Deno.exit(1);
    }

    this.printResults(results);
    console.log("\n✅ Benchmark completed successfully!");
  }
}

// Run benchmark if this file is executed directly
if (import.meta.main) {
  const benchmark = new ProxyBenchmark();
  await benchmark.runAllTests();
}
