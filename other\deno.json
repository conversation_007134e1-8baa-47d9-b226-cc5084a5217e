{"compilerOptions": {"allowJs": true, "lib": ["deno.window"], "strict": true}, "lint": {"rules": {"tags": ["recommended"]}}, "fmt": {"useTabs": false, "lineWidth": 100, "indentWidth": 2, "semiColons": true, "singleQuote": false, "proseWrap": "preserve"}, "tasks": {"start": "deno run --allow-net --allow-env main.ts", "dev": "deno run --allow-net --allow-env --watch main.ts", "check": "deno check main.ts", "lint": "deno lint main.ts", "fmt": "deno fmt main.ts"}}